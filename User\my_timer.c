#include "my_timer.h"

extern int basic_speed;

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int output_timer500ms = 0;

unsigned char intput_ff_flag;
unsigned int intput_timer500ms;

unsigned int led_timer500ms; // 每经过一个点，LED 点亮 500ms 后熄灭

unsigned char point_count = 0; // 经过的点位计数（入圈 + 1，出圈 + 1）

unsigned char system_mode = 1; // 系统状态（1 ~ 4 对应 4 道题目）

unsigned char circle_count = 0; // 第四题的圈数计数器

unsigned int distance = 0; // 记录小车每一段行驶的距离

extern uint8_t led_rgb[5];

void timer_init(void)
{
  HAL_TIM_Base_Start_IT(&htim2);
}

// TIM2 中断服务函数（1ms 中断）
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == htim2.Instance)
	{
		if(++measure_timer5ms >= 5)
		{
			measure_timer5ms = 0;
			
			Encoder_Task();
			distance += left_encoder.speed_cm_s;
//			Gray_Task();
//			PID_Task();
      hwt101_Task();
		}
		
//		if(pid_running != 1) return;
		 
		/* 出圈触发器 */
		if(Digtal != 0x00)
		{
			output_ff_flag = 1;
			if(++intput_timer500ms >= 500) intput_timer500ms = 500;
		}
		else if(output_ff_flag == 1 && intput_timer500ms == 500)
		{
			output_ff_flag = 0;
			intput_timer500ms = 0;
			point_count++;
			Car_State_Update();
		}
////		
////	  /* 入圈触发器 */
	  if(Digtal == 0x00)
	  {
	    intput_ff_flag = 1;
	    if(++output_timer500ms >= 500) output_timer500ms = 500;
	  }
	  else if(intput_ff_flag == 1 && output_timer500ms == 500)
	  {
	    intput_ff_flag = 0;
	    output_timer500ms = 0;
	    point_count++;
	    Car_State_Update();
	  }
		
		
		/* LED 状态检测 */
	  if(led_rgb[0] == 1 && ++led_timer500ms >= 500)
	  {
	    led_rgb[0] = 0;
	    led_timer500ms = 0;
	  }
	}
}

extern uint8_t stop_flat;
//每次点位计数更新时，根据系统状态同步更新小车的状态
void Car_State_Update(void)
{
  led_rgb[0] = 1;
  distance = 0;
  
  switch(system_mode)
  {
    case 1: // 第一题：直线行驶 A -> B
      if(point_count == 1)
      {
//        pid_running = 0;
////        motor_break();
				point_count = 0;
				stop_flat = 1;
      }
      break;
    case 2: // 第二题：环绕一圈 A -> B -> C -> D
      if(point_count == 1)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 2)
      {
        pid_control_mode = 0; // 使用角度环控制
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, 179);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1; 
      }
      break;
    case 3: // 第三题：8 字环绕一圈 A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // 使用角度环控制
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1;
      }
      break;
    case 4: // 第四题：8 字环绕四圈
      if(point_count == 1)
      {
        pid_control_mode = 1; // 使用循迹环控制
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // 使用角度环控制
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // 使用循迹环控制
      else if(point_count == 4)
      {
				pid_set_target(&pid_angle, 36);
        if(++circle_count >= 4)
        {
//          pid_running = 0;
//          motor_break();
					stop_flat = 1;
        }
        point_count = 0;
        pid_control_mode = 0; // 使用角度环控制
      }
      break;
  }
  
  /* 重置历史误差 */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
