#include "bno08x_app.h"
#include "bno08x_hal.h"

extern UART_HandleTypeDef huart1;
extern I2C_HandleTypeDef hi2c1;

float roll, pitch, yaw;
float convert_to_continuous_yaw(float current_yaw);
/**
 * @brief BNO080传感器完整初始化和配置函数
 * @note 包含硬件初始化、复位、传感器配置等完整流程
 * @note 用户可通过注释选择性启用不同功能模块
 * @retval 0: 成功, -1: 失败
 */
int8_t my_bno080_init(void)
{
    my_printf(&huart1, "开始初始化BNO080传感器...\n");

    // 1. 硬件初始化
    BNO080_Init(&hi2c1, BNO080_DEFAULT_ADDRESS);
    my_printf(&huart1, "BNO080 I2C初始化完成\n");

    // 2. 硬件复位（推荐使用，更可靠）
    if (BNO080_HardwareReset() == 0) {
        my_printf(&huart1, "BNO080硬件复位成功\n");
    } else {
        my_printf(&huart1, "BNO080硬件复位失败，尝试软件复位\n");
        // 备用方案：软件复位
        softReset();
        HAL_Delay(100);
        my_printf(&huart1, "BNO080软件复位完成\n");
    }

    // 3. 基础传感器配置（用户可选择性注释/取消注释）
    enableRotationVector(100);        // 启用旋转向量，100ms间隔
    my_printf(&huart1, "已启用旋转向量报告 (100ms)\n");

    // enableAccelerometer(50);          // 启用加速度计，50ms间隔
    // my_printf(&huart1, "已启用加速度计报告 (50ms)\n");

    // enableGyro(50);                   // 启用陀螺仪，50ms间隔
    // my_printf(&huart1, "已启用陀螺仪报告 (50ms)\n");

    // enableMagnetometer(100);          // 启用磁力计，100ms间隔
    // my_printf(&huart1, "已启用磁力计报告 (100ms)\n");

    // enableLinearAccelerometer(50);    // 启用线性加速度，50ms间隔
    // my_printf(&huart1, "已启用线性加速度报告 (50ms)\n");

    // enableStepCounter(1000);          // 启用步数计数，1000ms间隔
    // my_printf(&huart1, "已启用步数计数器 (1000ms)\n");

    // 4. 高级功能配置（可选）
    // enableGameRotationVector(20);     // 游戏模式旋转向量，20ms间隔
    // my_printf(&huart1, "已启用游戏旋转向量 (20ms)\n");

    // enableStabilityClassifier(500);   // 稳定性分类器，500ms间隔
    // my_printf(&huart1, "已启用稳定性分类器 (500ms)\n");

    HAL_Delay(200); // 等待传感器配置生效
    my_printf(&huart1, "BNO080传感器初始化完成！\n");

    return 0; // 初始化成功
}

uint8_t first_flat = 0;
float frist_yaw = 0;
/**
 * @brief BNO080传感器数据读取和处理任务
 * @note 适用于调度器系统，建议50-100ms执行周期
 * @note 用户可通过注释选择性启用不同数据输出
 */
void bno080_task(void)
{

    // 检查是否有新数据
    if (dataAvailable()) {

        // 1. 四元数和姿态数据（基础功能）
        float quat_i = getQuatI();
        float quat_j = getQuatJ();
        float quat_k = getQuatK();
        float quat_real = getQuatReal();
        QuaternionToEulerAngles(quat_i, quat_j, quat_k, quat_real, &roll, &pitch, &yaw);
				if(first_flat == 0)
				{
					first_flat = 1;
					frist_yaw = yaw;
				}
				yaw = yaw-frist_yaw;
	  
//        my_printf(&huart1, "Euler: %.2f, %.2f, %.2f\n", roll, pitch, yaw);

        // 2. 加速度数据（可选）
        // float accel_x = getAccelX();
        // float accel_y = getAccelY();
        // float accel_z = getAccelZ();
        // my_printf(&huart1, "Accel: %.3f, %.3f, %.3f g\n", accel_x, accel_y, accel_z);

        // 3. 陀螺仪数据（可选）
        // float gyro_x = getGyroX();
        // float gyro_y = getGyroY();
        // float gyro_z = getGyroZ();
        // my_printf(&huart1, "Gyro: %.3f, %.3f, %.3f rad/s\n", gyro_x, gyro_y, gyro_z);

        // 4. 磁力计数据（可选）
        // float mag_x = getMagX();
        // float mag_y = getMagY();
        // float mag_z = getMagZ();
        // my_printf(&huart1, "Mag: %.1f, %.1f, %.1f μT\n", mag_x, mag_y, mag_z);

        // 5. 精度监控（调试用）
        // uint8_t quat_accuracy = getQuatAccuracy();
        // uint8_t accel_accuracy = getAccelAccuracy();
        // uint8_t gyro_accuracy = getGyroAccuracy();
        // uint8_t mag_accuracy = getMagAccuracy();
        // my_printf(&huart1, "Accuracy: Q=%d A=%d G=%d M=%d\n",
        //           quat_accuracy, accel_accuracy, gyro_accuracy, mag_accuracy);

        // 6. 高级功能数据（可选）
        // uint16_t steps = getStepCount();
        // uint8_t stability = getStabilityClassifier();
        // my_printf(&huart1, "Steps: %d, Stability: %d\n", steps, stability);
    }
}

float get_roll(void)
{
	return roll;
}

float get_pitch(void)
{
	return pitch;
}

float get_yaw(void)
{
	float YAW = convert_to_continuous_yaw(yaw);
	return YAW;
}

// 使用静态变量来保存上一次的状态
float g_last_yaw = 0.0f;
int g_revolution_count = 0;
bool g_is_yaw_initialized = false;
/**
 * @brief 将一个在[-180, 180]范围内的yaw角度转换为连续的角度值。
 * 
 * @param current_yaw 从传感器读取的当前yaw值 (-180 to 180)。
 * @return float 连续的yaw角度值 (例如 370, -450 等)。
 */
float convert_to_continuous_yaw(float current_yaw) 
{
    // 定义一个阈值来检测“跳变”。这个值应该大于180，通常取270或300比较安全。
    const float WRAP_AROUND_THRESHOLD = 300.0f;

    // 首次调用时进行初始化
    if (!g_is_yaw_initialized) {
        g_last_yaw = current_yaw;
        g_is_yaw_initialized = true;
        g_revolution_count = 0;
    }

    // 计算与上次读数的差异
    float diff = current_yaw - g_last_yaw;

    // 检测是否发生了“跳变”
    if (diff > WRAP_AROUND_THRESHOLD) {
        // 从正角度跳到负角度 (例如, 从 170° 到 -175°), 实际是向右转, 圈数应该增加
        // 此时 diff 接近 -360 (例如 -175 - 170 = -345)
        // 这段逻辑处理的是从-180跳变到+180的情况，说明是向左转过界
        g_revolution_count--;
    } else if (diff < -WRAP_AROUND_THRESHOLD) {
        // 从负角度跳到正角度 (例如, 从 -170° 到 175°), 实际是向左转, 圈数应该减小
        // 此时 diff 接近 360 (例如 175 - (-170) = 345)
        // 这段逻辑处理的是从+180跳变到-180的情况，说明是向右转过界
        g_revolution_count++;
    }

    // 更新上次的yaw值以备下次调用
    g_last_yaw = current_yaw;

    // 计算连续的yaw值
    float continuous_yaw = current_yaw + (float)g_revolution_count * 360.0f;

    return continuous_yaw;
}
