#include "usart_app.h"
#include "jy901s_driver.h"

extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;

extern UART_HandleTypeDef huart5;

uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	// 初始化可变参数列表
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void uart_init(void)
{
	/* 串口环形缓冲区初始化：用于异步处理串口数据 */
  rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
	/*开启串口DMA空闲中断*/
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
}
	
/**
 * @brief UART DMA接收完成或空闲事件回调函数
 * @param huart UART句柄
 * @param Size 指示在事件发生前，DMA已经成功接收了多少字节的数据
 * @retval None
 */
// 统一的串口DMA接收完成回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART5)
    {
        // 调用HWT101的处理函数
        extern void hwt101_uart_callback(void);
        hwt101_uart_callback();
    }
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	// 1. 确认是目标串口 (USART1)
	if (huart->Instance == USART1)
	{
		// 2. 紧急停止当前的 DMA 传输 (如果还在进行中)
		//    因为空闲中断意味着发送方已经停止，防止 DMA 继续等待或出错
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);

		// 5. 清空 DMA 接收缓冲区，为下次接收做准备
		//    虽然 memcpy 只复制了 Size 个，但清空整个缓冲区更保险
		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		// 6. **关键：重新启动下一次 DMA 空闲接收**
		//    必须再次调用，否则只会接收这一次
		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

		// 7. 如果之前关闭了半满中断，可能需要在这里再次关闭 (根据需要)
		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	}
}


void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

//	my_printf(&huart1, "666\r\n");
	
	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);
	
	uint8_t num = 2;
	int8_t pwm = 2;
	sscanf(uart_dma_buffer, "set_%d:%d", &num, &pwm);
	if(num == 0)
	{
//		motor_set_l(pwm);
		pid_set_target(&pid_speed_left, pwm);
		my_printf(&huart1, "set_l_ok:%d\r\n", pwm);
	}
	else if(num == 1)
	{
		motor_set_r(pwm);
		pid_set_target(&pid_speed_right, pwm);
		my_printf(&huart1, "set_r_ok:%d\r\n", pwm);
	}
	else
	{
		my_printf(&huart1, "uart1:%s\r\n", uart_dma_buffer);
		my_printf(&huart5, "uart1:%s\r\n", uart_dma_buffer);
	}
		
	
	// 清空接收缓冲区
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}
