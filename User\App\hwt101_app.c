#include "hwt101_app.h"

#include "hwt101_driver.h"


extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart5;
extern DMA_HandleTypeDef hdma_usart5_rx;

uint8_t uart5_rx_buffer[32];
//测试使用
uint8_t uart5_flag = 0;

// HWT101传感器实例定义
HWT101_t hwt101;
// 处理后的角度值
float right_yaw = 0.0f;

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == UART5)
    {

        uart5_flag = 1;

        // 重新启动DMA接收 收满回调 定长数据
        HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
    }
}

void hwt101_init()
{
	// 启用串口5 HWT101专用串口
	HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));

    HWT101_Create(&hwt101,&huart5,1000);
    my_printf(&huart1,"Waiting...\r\n");
    HWT101_StartManualCalibration(&hwt101);
    HAL_Delay(5000);
    HWT101_StopManualCalibration(&hwt101);
    my_printf(&huart1,"OK\r\n");
    HWT101_ResetYaw(&hwt101);
    my_printf(&huart1,"YAW is OK\r\n");

}




void hwt101_Task(void)
{
	if(uart5_flag)
	{
		uart5_flag = 0;

        // 使用正确的缓冲区参数处理HWT101数据
        HWT101_ProcessBuffer(&hwt101, uart5_rx_buffer, sizeof(uart5_rx_buffer));
        float yaw = HWT101_GetYaw(&hwt101);
        float gyro_z = HWT101_GetGyroZ(&hwt101);
        HWT101_Data_t* data = HWT101_GetData(&hwt101);
        if(data != NULL && data->data_valid)  // 添加数据有效性检查
        {
            right_yaw = angle_extend_range(yaw); // 使用扩展角度范围，解决180°跳变问题
    //		my_printf(&huart1, "Yaw:%.2f, Gyro_z:%.2f\r\n", data->yaw, data->gyro_z);
    //		my_printf(&huart1, "right_Yaw:%.2f", right_yaw);
        }

		HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
	}
}

// 角度范围扩展函数：将-180°~+180°扩展到-360°~+360°，解决180°跳变问题
float angle_extend_range(float raw_angle)
{
    static float prev_angle = 0.0f;      // 上次角度值
    static int16_t jump_count = 0;       // 跳变累积次数
    static uint8_t first_call = 1;       // 首次调用标志

    if (first_call) { prev_angle = raw_angle; first_call = 0; return raw_angle; }
    if (prev_angle > 150.0f && raw_angle < -150.0f) jump_count++;
    else if (prev_angle < -150.0f && raw_angle > 150.0f) jump_count--;

    prev_angle = raw_angle;
    float extended = raw_angle + jump_count * 360.0f;
    return (extended > 360.0f) ? 360.0f : (extended < -360.0f) ? -360.0f : extended;
}










