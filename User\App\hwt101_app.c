#include "hwt101_app.h"

#include "hwt101_driver.h"


extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart5;
extern DMA_HandleTypeDef hdma_usart5_rx;

uint8_t uart5_rx_buffer[32];
//测试使用
uint8_t uart5_flag = 0;

// HWT101传感器实例定义
HWT101_t hwt101;
// 处理后的角度值
float right_yaw = 0.0f;

// HWT101串口5 DMA接收完成回调处理函数
void hwt101_uart_callback(void)
{
    uart5_flag = 1;
    // 重新启动DMA接收 收满回调 定长数据
    HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
}

void hwt101_init()
{
	// 启用串口5 HWT101专用串口
	HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));

    // 创建HWT101实例
    HWT101_Create(&hwt101,&huart5,1000);
    my_printf(&huart1,"HWT101 Init...\r\n");

    // 快速校准：缩短校准时间以避免系统初始化阻塞
    HWT101_StartManualCalibration(&hwt101);
    HAL_Delay(2000);  // 缩短为2秒
    HWT101_StopManualCalibration(&hwt101);

    // Z轴角度归零
    HWT101_ResetYaw(&hwt101);
    my_printf(&huart1,"HWT101 Ready\r\n");
}




void hwt101_Task(void)
{
	if(uart5_flag)
	{
		uart5_flag = 0;

        // 使用正确的缓冲区参数处理HWT101数据
        if(HWT101_ProcessBuffer(&hwt101, uart5_rx_buffer, sizeof(uart5_rx_buffer)) == 0)
        {
            float yaw = HWT101_GetYaw(&hwt101);
            HWT101_Data_t* data = HWT101_GetData(&hwt101);

            // 数据有效性和范围检查
            if(data != NULL && data->data_valid && yaw >= -180.0f && yaw <= 180.0f)
            {
                right_yaw = angle_extend_range(yaw); // 使用扩展角度范围，解决180°跳变问题
        //		my_printf(&huart1, "Yaw:%.2f, Gyro_z:%.2f\r\n", data->yaw, data->gyro_z);
        //		my_printf(&huart1, "right_Yaw:%.2f", right_yaw);
            }
        }

		HAL_UART_Receive_DMA(&huart5, uart5_rx_buffer, sizeof(uart5_rx_buffer));
	}
}

// 角度范围扩展函数：将-180°~+180°扩展到连续角度，解决180°跳变问题
float angle_extend_range(float raw_angle)
{
    static float prev_angle = 0.0f;      // 上次角度值
    static int16_t jump_count = 0;       // 跳变累积次数
    static uint8_t first_call = 1;       // 首次调用标志

    // 首次调用初始化
    if (first_call) {
        prev_angle = raw_angle;
        first_call = 0;
        return raw_angle;
    }

    // 检测正向跳变（从+180°附近跳到-180°附近）
    if (prev_angle > 150.0f && raw_angle < -150.0f) {
        jump_count++;
    }
    // 检测反向跳变（从-180°附近跳到+180°附近）
    else if (prev_angle < -150.0f && raw_angle > 150.0f) {
        jump_count--;
    }

    prev_angle = raw_angle;
    float extended = raw_angle + jump_count * 360.0f;

    // 限制扩展范围在合理区间内，避免累积误差
    if (extended > 720.0f) extended = 720.0f;
    else if (extended < -720.0f) extended = -720.0f;

    return extended;
}










